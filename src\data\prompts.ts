export interface PromptItem {
  id: string;
  name: string;
  description: string;
  prompt: string;
  icon: string;
}



// 預設提示詞資料
export const DEFAULT_PROMPTS: PromptItem[] = [
  {
    id: 'computer-desk-figurine',
    name: '電腦桌公仔',
    description: '放置在電腦桌上的精緻公仔模型',
    prompt: "create a 1/7 scale commercialized figurine of the characters in the picture, in a realistic style, in a real environment. The figurine is placed on a computer desk. The figurine has a round transparent acrylic base, with no text on the base. The content on the computer screen is the Zbrush modelling process of this figurine. Illustrations. Please turn this photo into a figure. Behind it, there should be a Model packaging box with the character from this photo printed on it. In front of the box, on a round plastic base, place the figure version of the photo I gave you. I'd like the PVC material to be clearly represented.It would be even better if the background is indoors.",
    icon: 'pi-user'
  },
  {
    id: 'ghibli-style',
    name: '吉卜力風格',
    description: '將圖片轉換為吉卜力動畫風格',
    prompt: '請將照片生成吉卜力風格。',
    icon: 'pi-palette'
  },
  {
    id: 'anime-character',
    name: '動漫角色',
    description: '可愛的動漫風格角色',
    prompt: '一個可愛的動漫風格角色，大眼睛，柔和的色彩，細緻的線條，高品質插畫風格',
    icon: 'pi-user'
  },
  {
    id: 'cyberpunk-city',
    name: '賽博龐克城市',
    description: '未來科技感的城市夜景',
    prompt: '賽博龐克風格的未來城市夜景，霓虹燈光，高樓大廈，科技感十足，雨夜氛圍，電影級畫質',
    icon: 'pi-image'
  },
  {
    id: 'watercolor-landscape',
    name: '水彩風景',
    description: '柔美的水彩畫風景',
    prompt: '美麗的水彩畫風景，柔和的色彩暈染，自然的筆觸，寧靜的氛圍，藝術感十足',
    icon: 'pi-image'
  },
  {
    id: 'minimalist-design',
    name: '極簡設計',
    description: '簡潔現代的設計風格',
    prompt: '極簡主義設計風格，簡潔的線條，純淨的色彩，現代感，留白空間，高級質感',
    icon: 'pi-palette'
  },
  {
    id: 'fantasy-creature',
    name: '奇幻生物',
    description: '神秘的奇幻世界生物',
    prompt: '神秘的奇幻生物，魔法光芒，精緻的細節，夢幻的色彩，高品質3D渲染效果',
    icon: 'pi-user'
  },
  {
    id: 'vintage-poster',
    name: '復古海報',
    description: '經典復古風格海報設計',
    prompt: '復古風格海報設計，經典的色彩搭配，懷舊的質感，藝術字體，手工插畫風格',
    icon: 'pi-palette'
  }
];

// 根據名稱或描述搜尋提示詞
export function searchPrompts(query: string): PromptItem[] {
  const lowerQuery = query.toLowerCase();
  return DEFAULT_PROMPTS.filter(prompt =>
    prompt.name.toLowerCase().includes(lowerQuery) ||
    prompt.description.toLowerCase().includes(lowerQuery)
  );
}

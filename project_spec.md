# Gemini 圖片生成應用程式 - 開發規格書

## 1. 專案概述

本專案為一個基於 Next.js 的網頁應用程式，旨在提供使用者一個簡潔直觀的介面，透過 Google Gemini API 進行圖片生成。應用程式將支援文字轉圖片，以及文字結合參考圖片（可選）生成圖片的功能。

## 2. 技術棧

* **前端框架：** Next.js (App Router)
* **程式語言：** TypeScript
* **樣式框架：** Tailwind CSS
* **UI 元件庫：** PrimeReact
* **狀態管理：** Zustand (建議)
* **API 請求：** Fetch API

## 3. 功能需求

### 3.1. API 金鑰設定

* 使用者可在應用程式中輸入並儲存自己的 Gemini API Key。
* 儲存方式：使用瀏覽器 `localStorage` 進行暫存。
* 驗證：輸入後應立即進行簡單的 API 驗證，並顯示成功或失敗訊息。
* 介面：一個輸入框和一個「儲存」按鈕。

### 3.2. 模型選擇

* 提供一個下拉式選單，讓使用者選擇不同的 Gemini 模型。
* **預設模型：** `gemini-2.5-flash-image-preview`。
* **支援模型：**
  * 圖片生成模型（例如：`imagen-3.0-generate-002`）
  * 其他未來可能推出的模型
* 使用者選擇的模型應被儲存，並在下次開啟應用程式時自動載入。

### 3.3. 圖片生成

* **文字轉圖片：**
  * 使用者輸入提示詞（prompt）到文字區域。
  * 點擊「生成圖片」按鈕後，呼叫 Gemini API 進行圖片生成。
  * API 請求格式：`POST` to `/v1beta/models/{model_name}:generateContent` with `text` part.
* **文字 + 參考圖轉圖片：**
  * 提供一個上傳按鈕，讓使用者選擇一張參考圖片。
  * 上傳的圖片應在前端進行預覽。
  * 圖片上傳後，點擊「生成圖片」按鈕。
  * API 請求格式：`POST` to `/v1beta/models/{model_name}:generateContent` with `text` and `inlineData` parts.
* **進度與結果顯示：**
  * 在圖片生成過程中，顯示一個載入中的動畫或訊息。
  * 圖片生成完成後，將生成的圖片顯示在頁面上。
  * 應提供「下載圖片」功能。

### 3.4. 提示詞微調功能 (新功能)

* **功能概述：** 讓使用者可以在一個基礎提示詞（由應用程式設定）的基礎上，新增自己的微調內容，例如「穿著牛仔褲」、「黃色的上衣」等。
* **介面：**
  * 在主要提示詞輸入區塊下方，新增一個可選的文字輸入框，標註為「微調提示詞（選填）」。
* **程式邏輯：**
  1. **組合提示詞：** 在呼叫圖片生成 API 前，應用程式會先將「基礎提示詞」與使用者輸入的「微調提示詞」組合。
  2. **中繼呼叫（Intermediate Call）：** * 應用程式將這兩個提示詞發送給 Gemini  **文字模型** （例如：`gemini-2.0-flash`），並要求它將兩個提示詞組合成一個更具體、更完整的最終提示詞。
     * **範例提示：** `請將以下兩個提示詞合併為一個用於圖片生成的完整且精確的提示詞。基礎提示詞：「一個可愛的公仔，背景是城市夜景」。微調提示詞：「穿著牛仔褲，戴著黃色的帽子」。`
  3. **圖片生成呼叫：** * 取得中繼呼叫返回的最終提示詞。
     * 使用這個最終提示詞，向 Gemini **圖片生成模型**發出圖片生成請求。
* **錯誤處理：**
  * 若中繼呼叫失敗，應顯示錯誤訊息，並可選擇直接使用原始提示詞進行圖片生成。

## 4. UI/UX 設計

### 4.1. 整體風格

* **主題色：** 採用明亮、活潑、令人愉悅的配色方案，例如天藍色、嫩黃色或薄荷綠等。
* **佈局：**
  * 介面應簡潔，並將功能區塊清晰分隔。
  * 圖片生成區域應有足夠的留白。
* **響應式設計：**
  * 介面應能在不同尺寸的裝置上（電腦、平板、手機）良好顯示。
  * 在手機上，功能區塊應以堆疊方式呈現。

### 4.2. 元件設計

* **輸入框：** 使用 PrimeReact 的 `InputText` 或 `InputTextarea`，可搭配圓角、陰影等效果。
* **按鈕：** 使用 PrimeReact 的 `Button`，並設計成具備亮麗漸層或動態效果的樣式。
* **下拉式選單：** 使用 PrimeReact 的 `Dropdown`。
* **圖片預覽區：** 上傳的參考圖片和生成的結果圖片應有清晰的邊框和標題。
* **狀態訊息：** 使用浮動訊息或 Toast 來顯示 API 驗證、載入中等狀態。

## 5. 檔案與資料夾結構

```
/src
├── app/
│   ├── layout.tsx
│   ├── page.tsx
├── components/
│   ├── ImageGenerator.tsx
│   ├── APIKeyInput.tsx
│   ├── ModelSelector.tsx
├── styles/
│   ├── globals.css
├── utils/
│   ├── gemini-api.ts
├── hooks/
│   ├── useGemini.ts

```

## 6. 開發與部署

* **開發：** 使用 `npm run dev`進行開發。
* **部署：** 應用程式可部署在 Vercel 或其他支援 Next.js 的平台。
*

{"name": "gemini-image", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "15.5.2", "primeicons": "^7.0.0", "primereact": "^10.9.7", "react": "19.1.0", "react-dom": "19.1.0", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "tailwindcss": "^4", "typescript": "^5"}}
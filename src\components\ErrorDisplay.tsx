'use client';

import React from 'react';
import { Message } from 'primereact/message';
import { Button } from 'primereact/button';

interface ErrorDisplayProps {
  error: string | null;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
}

export default function ErrorDisplay({
  error,
  onRetry,
  onDismiss,
  className = ''
}: ErrorDisplayProps) {
  if (!error) return null;

  // 檢查是否為配額錯誤
  const isQuotaError = error.includes('配額') || error.includes('RESOURCE_EXHAUSTED') || error.includes('quota');

  // 檢查是否為權限錯誤
  const isPermissionError = error.includes('權限') || error.includes('PERMISSION_DENIED') || error.includes('API Key');

  // 檢查是否為網路錯誤
  const isNetworkError = error.includes('網路') || error.includes('連線') || error.includes('Network');

  const getErrorIcon = () => {
    if (isQuotaError) return 'pi-clock';
    if (isPermissionError) return 'pi-key';
    if (isNetworkError) return 'pi-wifi';
    return 'pi-exclamation-triangle';
  };

  const getErrorSeverity = () => {
    if (isQuotaError) return 'warn';
    if (isPermissionError) return 'error';
    if (isNetworkError) return 'info';
    return 'error';
  };

  const getErrorTitle = () => {
    if (isQuotaError) return 'API 配額限制';
    if (isPermissionError) return 'API Key 問題';
    if (isNetworkError) return '網路連線問題';
    return '發生錯誤';
  };

  const getErrorSuggestions = () => {
    if (isQuotaError) {
      return [
        '請稍後再試（通常配額會在一段時間後重置）',
        '檢查您的 Google Cloud Console 配額設定',
        '考慮升級您的 API 使用方案'
      ];
    }

    if (isPermissionError) {
      return [
        '檢查 API Key 是否正確輸入',
        '確認 API Key 具有圖片生成權限',
        '檢查 API Key 是否已過期'
      ];
    }

    if (isNetworkError) {
      return [
        '檢查網路連線是否正常',
        '嘗試重新整理頁面',
        '確認防火牆沒有阻擋連線'
      ];
    }

    return [
      '請稍後再試',
      '如果問題持續發生，請檢查 API Key 設定'
    ];
  };

  return (
    <div className={`error-display ${className}`}>
      <Message
        severity={getErrorSeverity()}
        className="w-full"
        pt={{
          root: { className: 'border-l-4 p-4 rounded-r-lg' },
          icon: { className: `pi ${getErrorIcon()} text-lg` }
        }}
      >
        <div className="flex flex-col gap-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className="font-semibold text-lg mb-2">{getErrorTitle()}</h4>
              <p className="text-sm mb-3">{error}</p>
            </div>

            {onDismiss && (
              <Button
                icon="pi pi-times"
                onClick={onDismiss}
                className="p-1 text-gray-500 hover:text-gray-700 bg-transparent border-0"
                tooltip="關閉"
                size="small"
              />
            )}
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <h5 className="font-medium text-sm mb-2 text-gray-700">💡 建議解決方案：</h5>
            <ul className="text-xs text-gray-600 space-y-1">
              {getErrorSuggestions().map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>

          {onRetry && (
            <div className="flex justify-end">
              <Button
                label="重試"
                icon="pi pi-refresh"
                onClick={onRetry}
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white border-0 rounded-lg text-sm"
              />
            </div>
          )}
        </div>
      </Message>
    </div>
  );
}

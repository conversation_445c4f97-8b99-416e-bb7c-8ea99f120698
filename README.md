# Gemini 圖片生成

一個基於 Next.js 的網頁應用程式，使用 Google Gemini AI 進行圖片生成。支援文字轉圖片和參考圖片生成功能。

## 功能特色

- 🎨 **文字轉圖片**：輸入描述文字，AI 自動生成對應圖片
- 🖼️ **多元圖片上傳**：支援檔案選擇、拖拉上傳、複製貼上三種方式
- 🔧 **提示詞微調**：智能組合基礎提示詞和微調提示詞
- 📝 **預設提示詞**：內建多種精選提示詞模板，快速開始創作
- 🎛️ **直觀介面**：提示詞設定永遠可見，操作更直觀
- 🎯 **多模型支援**：支援多種 Gemini 圖片生成模型
- 🚫 **智能錯誤處理**：針對不同錯誤類型提供本地化訊息和解決建議
- 📱 **響應式設計**：適配電腦、平板、手機等各種裝置
- 🎨 **美觀介面**：明亮活潑的配色方案和流暢動畫

## 技術棧

- **前端框架**：Next.js 15 (App Router)
- **程式語言**：TypeScript
- **樣式框架**：Tailwind CSS
- **UI 元件庫**：PrimeReact
- **狀態管理**：Zustand
- **API 請求**：Fetch API

## 快速開始

### 1. 安裝依賴

```bash
npm install
```

### 2. 啟動開發伺服器

```bash
npm run dev
```

### 3. 開啟瀏覽器

前往 [http://localhost:3000](http://localhost:3000) 查看應用程式。

### 4. 設定 API Key

1. 前往 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登入您的 Google 帳號
3. 建立新的 API Key
4. 在應用程式中輸入並驗證 API Key

## 使用方法

### 基本圖片生成

1. **設定 API Key**：在「API Key 設定」區域輸入您的 Gemini API Key
2. **選擇模型**：在「模型選擇」區域選擇適合的圖片生成模型
3. **設定提示詞**：
   - 從下拉選單選擇預設提示詞，或輸入自訂描述
   - 可選擇性加入微調提示詞來增加細節
4. **上傳參考圖片**（選填）：
   - **檔案選擇**：點擊「選擇檔案」按鈕
   - **拖拉上傳**：將圖片檔案拖拉到上傳區域
   - **複製貼上**：複製圖片後按 Ctrl+V (或 Cmd+V) 貼上
5. **生成圖片**：點擊「生成圖片」按鈕

### 進階功能

#### 預設提示詞

- 內建多種精選提示詞模板，包含：
  - **電腦桌公仔**：專業級公仔產品攝影
  - **吉卜力風格**：經典動畫風格轉換
  - **動漫角色**、**賽博龐克城市**、**水彩風景** 等多種風格
- 從下拉選單選擇後自動填入基礎提示詞欄位

#### 多元圖片上傳

- **檔案選擇**：傳統的檔案瀏覽器選擇方式
- **拖拉上傳**：直接將圖片檔案拖拉到上傳區域
- **複製貼上**：從剪貼簿直接貼上圖片（支援 Ctrl+V / Cmd+V）
- **即時驗證**：自動檢查檔案類型和大小，提供即時回饋

#### 智能錯誤處理
- **429 配額錯誤**：顯示配額用完提示和解決建議
- **401/403 權限錯誤**：顯示 API Key 問題和檢查建議
- **網路錯誤**：顯示連線問題和故障排除建議
- **重試功能**：提供一鍵重試按鈕
- **關閉功能**：可手動關閉錯誤訊息
- **本地化訊息**：所有錯誤都有中文說明和解決方案

#### 提示詞微調

- 在「微調提示詞」欄位加入額外的細節描述
- AI 會自動組合基礎提示詞和微調提示詞，生成更精確的描述

#### 參考圖片

- 點擊「選擇圖片」上傳參考圖片
- 結合文字描述和參考圖片生成新的圖片

#### 下載圖片

- 圖片生成完成後，點擊「下載圖片」保存到本地

## 支援的模型

- **Gemini 2.0 Flash Preview Image Generation**：最新的圖片生成模型，預設選擇，具備優秀的生成品質和速度（已優化 API 相容性）
- **Gemini 2.5 Flash Image Preview**：穩定的圖片生成模型，速度快且效果佳
- **Imagen 3.0 Generate**：高品質圖片生成，適合精細創作
- **Gemini 2.0 Flash**：文字模型，用於提示詞組合

## 專案結構

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # 全域佈局
│   ├── page.tsx           # 主頁面
│   └── globals.css        # 全域樣式
├── components/            # React 元件
│   ├── APIKeyInput.tsx    # API Key 輸入元件
│   ├── ModelSelector.tsx  # 模型選擇元件
│   ├── ImageGenerator.tsx # 圖片生成核心元件
│   ├── ImageUpload.tsx    # 多元圖片上傳元件
│   └── ErrorDisplay.tsx   # 智能錯誤顯示元件
├── data/                  # 資料檔案
│   └── prompts.ts         # 提示詞資料結構
├── hooks/                 # 自定義 Hooks
│   └── useGemini.ts       # Zustand 狀態管理
├── utils/                 # 工具函數
│   └── gemini-api.ts      # Gemini API 通訊
└── __tests__/             # 測試檔案
    ├── gemini-api.test.ts
    ├── useGemini.test.ts
    ├── prompts.test.ts
    ├── ImageUpload.test.tsx
    ├── error-handling.test.ts
    └── ErrorDisplay.test.tsx
```

## 可用腳本

```bash
# 開發模式
npm run dev

# 建置專案
npm run build

# 啟動生產伺服器
npm start

# 程式碼檢查
npm run lint

# 執行測試
npm test

# 測試覆蓋率
npm run test:coverage
```

## 部署

### Vercel 部署

1. 將專案推送到 GitHub
2. 前往 [Vercel](https://vercel.com)
3. 匯入您的 GitHub 專案
4. 自動部署完成

### 其他平台

本專案支援任何支援 Next.js 的部署平台，如 Netlify、Railway 等。

## 注意事項

- 需要有效的 Google Gemini API Key
- API Key 儲存在瀏覽器本地，不會上傳到伺服器
- 圖片生成需要網路連線
- 建議使用現代瀏覽器以獲得最佳體驗

## 故障排除

### API 錯誤處理

- **400 錯誤 (INVALID_ARGUMENT)**：應用程式已自動處理不同模型的 API 格式差異
- **新模型相容性**：`gemini-2.0-flash-preview-image-generation` 模型已優化，支援 IMAGE+TEXT 回應模式
- **API Key 問題**：請確保 API Key 有效且具備圖片生成權限

### 常見問題

- **圖片生成失敗**：檢查網路連線和 API Key 權限
- **模型選擇**：建議使用預設的 `gemini-2.0-flash-preview-image-generation` 模型
- **提示詞優化**：使用預設提示詞模板可獲得更好的生成效果

## 授權

MIT License

## 貢獻

歡迎提交 Issue 和 Pull Request！

/* @import "tailwindcss"; */

@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme);
@import "tailwindcss/utilities.css" layer(utilities);

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* 明亮活潑的配色方案 */
  --primary-blue: #3b82f6;
  --primary-purple: #8b5cf6;
  --primary-pink: #ec4899;
  --primary-green: #10b981;
  --primary-yellow: #f59e0b;
  --primary-cyan: #06b6d4;

  /* 漸層背景 */
  --gradient-bg: linear-gradient(135deg, #dbeafe 0%, #f3e8ff 50%, #fce7f3 100%);
  --gradient-card: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --gradient-bg: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
    --gradient-card: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-purple), var(--primary-pink));
}

/* 動畫效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* PrimeReact 元件自定義樣式 */
.p-card {
  border-radius: 12px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.3s ease !important;
}

.p-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.p-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.p-button:hover {
  transform: translateY(-1px) !important;
}

.p-inputtext, .p-inputtextarea {
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.p-inputtext:focus, .p-inputtextarea:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.p-dropdown {
  border-radius: 8px !important;
}

.p-fileupload .p-fileupload-buttonbar {
  border-radius: 8px 8px 0 0 !important;
}

.p-fileupload .p-fileupload-content {
  border-radius: 0 0 8px 8px !important;
}

/* 響應式設計增強 */
@media (max-width: 768px) {
  .p-card {
    margin: 0.5rem !important;
  }

  .p-button {
    width: 100% !important;
    justify-content: center !important;
  }
}

/* 圖片預覽響應式設計 */
.image-preview-container {
  max-width: 100%;
  overflow: hidden;
}

.image-preview-container img {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

@media (max-width: 768px) {
  .image-preview-container {
    max-height: 70vh;
  }

  .image-preview-container img {
    max-height: 70vh;
  }
}

@media (max-width: 480px) {
  .image-preview-container {
    max-height: 60vh;
  }

  .image-preview-container img {
    max-height: 60vh;
  }
}

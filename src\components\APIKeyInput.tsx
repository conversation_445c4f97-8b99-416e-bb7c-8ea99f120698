'use client';

import React, { useState } from 'react';

import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Message } from 'primereact/message';
import { Password } from 'primereact/password';
import { useApiKey } from '@/hooks/useGemini';
import { validateApiKey } from '@/utils/gemini-api';

interface APIKeyInputProps {
  className?: string;
}

export default function APIKeyInput({ className = '' }: APIKeyInputProps) {
  const { apiKey, isApiKeyValid, setApiKey, setApiKeyValid } = useApiKey();
  const [inputValue, setInputValue] = useState(apiKey);
  const [isValidating, setIsValidating] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string | null>(null);
  const [messageType, setMessageType] = useState<'success' | 'error' | 'info'>('info');

  const handleValidateAndSave = async () => {
    if (!inputValue.trim()) {
      setValidationMessage('請輸入 API Key');
      setMessageType('error');
      return;
    }

    setIsValidating(true);
    setValidationMessage('正在驗證 API Key...');
    setMessageType('info');

    try {
      const result = await validateApiKey(inputValue.trim());

      if (result.isValid) {
        setApiKey(inputValue.trim());
        setApiKeyValid(true);
        setValidationMessage('API Key 驗證成功！');
        setMessageType('success');
      } else {
        setApiKeyValid(false);
        setValidationMessage(result.error || 'API Key 驗證失敗，請檢查是否正確');
        setMessageType('error');
      }
    } catch (error) {
      setApiKeyValid(false);
      setValidationMessage('驗證過程中發生錯誤，請稍後再試');
      setMessageType('error');
      console.error('API Key 驗證錯誤:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    // 清除之前的驗證訊息
    if (validationMessage) {
      setValidationMessage(null);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleValidateAndSave();
    }
  };

  return (
    <Card
      title="API Key 設定"
      className={`w-full ${className}`}
      pt={{
        root: { className: 'shadow-lg border-0' },
        title: { className: 'text-xl font-bold text-blue-600 mb-4' },
        content: { className: 'pt-0' }
      }}
    >
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <label htmlFor="api-key" className="font-medium text-gray-700">
            Gemini API Key
          </label>
          <div className="flex gap-2">
            <Password
              id="api-key"
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="請輸入您的 Gemini API Key"
              className="flex-1"
              inputClassName="w-full"
              feedback={false}
              toggleMask
              pt={{
                input: {
                  className: 'w-full p-3 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                }
              }}
            />
            <Button
              label={isValidating ? '驗證中...' : '儲存'}
              onClick={handleValidateAndSave}
              loading={isValidating}
              disabled={!inputValue.trim() || isValidating}
              className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors"
              pt={{
                root: { className: 'border-0' }
              }}
            />
          </div>
        </div>

        {validationMessage && (
          <Message
            severity={messageType}
            text={validationMessage}
            className="w-full"
            pt={{
              root: { className: 'border-l-4 p-3 rounded-r-lg' }
            }}
          />
        )}

        {isApiKeyValid && (
          <div className="flex items-center gap-2 text-green-600">
            <i className="pi pi-check-circle"></i>
            <span className="font-medium">API Key 已設定並驗證成功</span>
          </div>
        )}

        <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
          <div className="flex items-start gap-2">
            <i className="pi pi-info-circle mt-0.5 text-blue-500"></i>
            <div>
              <p className="font-medium mb-1">如何取得 API Key：</p>
              <ol className="list-decimal list-inside space-y-1 text-xs">
                <li>前往 <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Google AI Studio</a></li>
                <li>登入您的 Google 帳號</li>
                <li>點擊「Create API Key」建立新的 API Key</li>
                <li>複製 API Key 並貼上到上方輸入框</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}

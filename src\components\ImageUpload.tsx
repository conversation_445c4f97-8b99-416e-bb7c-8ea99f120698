'use client';

import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Button } from 'primereact/button';
import { Image } from 'primereact/image';
import { Toast } from 'primereact/toast';
import { useReferenceImage } from '@/hooks/useGemini';

interface ImageUploadProps {
  className?: string;
}

export default function ImageUpload({ className = '' }: ImageUploadProps) {
  const { referenceImage, referenceImagePreview, setReferenceImage, setReferenceImagePreview } = useReferenceImage();
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useRef<Toast>(null);

  // 處理檔案選擇
  const handleFileSelect = useCallback((file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.current?.show({
        severity: 'error',
        summary: '檔案類型錯誤',
        detail: '請選擇圖片檔案',
        life: 3000
      });
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB
      toast.current?.show({
        severity: 'error',
        summary: '檔案過大',
        detail: '圖片檔案大小不能超過 10MB',
        life: 3000
      });
      return;
    }

    setReferenceImage(file);

    // 建立預覽
    const reader = new FileReader();
    reader.onload = (e) => {
      setReferenceImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    toast.current?.show({
      severity: 'success',
      summary: '圖片上傳成功',
      detail: `已選擇圖片：${file.name}`,
      life: 3000
    });
  }, [setReferenceImage, setReferenceImagePreview]);

  // 處理檔案輸入
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // 處理拖拉進入
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  // 處理拖拉離開
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  };

  // 處理拖拉懸停
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // 處理拖拉放下
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => file.type.startsWith('image/'));

    if (imageFile) {
      handleFileSelect(imageFile);
    } else {
      toast.current?.show({
        severity: 'warn',
        summary: '無效檔案',
        detail: '請拖拉圖片檔案',
        life: 3000
      });
    }
  };

  // 處理複製貼上
  const handlePaste = useCallback((e: ClipboardEvent) => {
    const items = Array.from(e.clipboardData?.items || []);
    const imageItem = items.find(item => item.type.startsWith('image/'));

    if (imageItem) {
      const file = imageItem.getAsFile();
      if (file) {
        handleFileSelect(file);
      }
    }
  }, [handleFileSelect]);

  // 註冊全域貼上事件
  useEffect(() => {
    document.addEventListener('paste', handlePaste);
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [handlePaste]);

  // 移除圖片
  const handleRemoveFile = () => {
    setReferenceImage(null);
    setReferenceImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    toast.current?.show({
      severity: 'info',
      summary: '圖片已移除',
      detail: '參考圖片已清除',
      life: 2000
    });
  };

  // 點擊選擇檔案
  const handleChooseFile = () => {
    fileInputRef.current?.click();
  };

  return (
    <>
      <Toast ref={toast} />
      <div className={`flex flex-col gap-2 ${className}`}>
        <label className="font-medium text-gray-700">
          參考圖片 <span className="text-gray-400">(選填)</span>
        </label>

        {!referenceImagePreview ? (
          <div
            className={`
              relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200
              ${isDragOver
                ? 'border-green-500 bg-green-50 scale-105'
                : 'border-gray-300 hover:border-green-400 hover:bg-gray-50'
              }
            `}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={handleChooseFile}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileInputChange}
              className="hidden"
            />

            <div className="flex flex-col items-center gap-4">
              <div className={`text-4xl ${isDragOver ? 'text-green-500' : 'text-gray-400'}`}>
                <i className="pi pi-cloud-upload"></i>
              </div>

              <div className="space-y-2">
                <p className={`font-medium ${isDragOver ? 'text-green-700' : 'text-gray-700'}`}>
                  {isDragOver ? '放開以上傳圖片' : '選擇或拖拉圖片到此處'}
                </p>
                <p className="text-sm text-gray-500">
                  支援 JPG、PNG、GIF 等格式，最大 10MB
                </p>
                <p className="text-xs text-blue-600">
                  💡 提示：您也可以直接 <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Ctrl+V</kbd> 貼上剪貼簿中的圖片
                </p>
              </div>

              <Button
                label="選擇檔案"
                icon="pi pi-folder-open"
                className="bg-green-500 hover:bg-green-600 text-white border-0 px-6 py-2 rounded-lg"
                onClick={(e) => {
                  e.stopPropagation();
                  handleChooseFile();
                }}
              />
            </div>
          </div>
        ) : (
          <div className="border border-gray-300 rounded-lg p-4 bg-gray-50">
            <div className="flex items-center gap-4">
              <Image
                src={referenceImagePreview}
                alt="參考圖片預覽"
                width="100"
                height="100"
                className="rounded-lg object-cover"
              />
              <div className="flex-1">
                <p className="font-medium text-gray-700">已選擇參考圖片</p>
                <p className="text-sm text-gray-500">{referenceImage?.name}</p>
                <p className="text-xs text-gray-400 mt-1">
                  大小：{referenceImage ? (referenceImage.size / 1024 / 1024).toFixed(2) : '0'} MB
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <Button
                  icon="pi pi-times"
                  onClick={handleRemoveFile}
                  className="p-2 bg-red-500 hover:bg-red-600 text-white border-0 rounded-lg"
                  tooltip="移除圖片"
                />
                <Button
                  icon="pi pi-refresh"
                  onClick={handleChooseFile}
                  className="p-2 bg-blue-500 hover:bg-blue-600 text-white border-0 rounded-lg"
                  tooltip="重新選擇"
                />
              </div>
            </div>
          </div>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <p>📁 <strong>檔案選擇：</strong>點擊「選擇檔案」按鈕</p>
          <p>🖱️ <strong>拖拉上傳：</strong>將圖片檔案拖拉到上方區域</p>
          <p>📋 <strong>複製貼上：</strong>複製圖片後按 Ctrl+V (或 Cmd+V) 貼上</p>
        </div>
      </div>
    </>
  );
}

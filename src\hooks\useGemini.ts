import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 支援的 Gemini 模型
export const GEMINI_MODELS = [
  {
    value: 'gemini-2.0-flash-preview-image-generation',
    label: 'Gemini 2.0 Flash Preview Image Generation',
    type: 'image'
  },
  {
    value: 'gemini-2.5-flash-image-preview',
    label: 'Gemini 2.5 Flash Image Preview',
    type: 'image'
  },
  {
    value: 'imagen-3.0-generate-002',
    label: 'Imagen 3.0 Generate',
    type: 'image'
  },
  {
    value: 'gemini-2.0-flash',
    label: 'Gemini 2.0 Flash',
    type: 'text'
  }
] as const;

export type GeminiModel = typeof GEMINI_MODELS[number]['value'];

// 應用程式狀態介面
interface GeminiState {
  // API Key 相關
  apiKey: string;
  isApiKeyValid: boolean;

  // 模型選擇
  selectedModel: GeminiModel;

  // 圖片生成狀態
  isGenerating: boolean;
  generatedImage: string | null;

  // 提示詞
  prompt: string;
  refinementPrompt: string;

  // 參考圖片
  referenceImage: File | null;
  referenceImagePreview: string | null;

  // 錯誤訊息
  error: string | null;



  // Actions
  setApiKey: (apiKey: string) => void;
  setApiKeyValid: (isValid: boolean) => void;
  setSelectedModel: (model: GeminiModel) => void;
  setIsGenerating: (isGenerating: boolean) => void;
  setGeneratedImage: (image: string | null) => void;
  setPrompt: (prompt: string) => void;
  setRefinementPrompt: (prompt: string) => void;
  setReferenceImage: (file: File | null) => void;
  setReferenceImagePreview: (preview: string | null) => void;
  setError: (error: string | null) => void;
  clearAll: () => void;
}

// 建立 Zustand store
export const useGeminiStore = create<GeminiState>()(
  persist(
    (set) => ({
      // 初始狀態
      apiKey: '',
      isApiKeyValid: false,
      selectedModel: 'gemini-2.0-flash-preview-image-generation',
      isGenerating: false,
      generatedImage: null,
      prompt: '',
      refinementPrompt: '',
      referenceImage: null,
      referenceImagePreview: null,
      error: null,

      // Actions
      setApiKey: (apiKey: string) => set({ apiKey }),
      setApiKeyValid: (isValid: boolean) => set({ isApiKeyValid: isValid }),
      setSelectedModel: (model: GeminiModel) => set({ selectedModel: model }),
      setIsGenerating: (isGenerating: boolean) => set({ isGenerating }),
      setGeneratedImage: (image: string | null) => set({ generatedImage: image }),
      setPrompt: (prompt: string) => set({ prompt }),
      setRefinementPrompt: (refinementPrompt: string) => set({ refinementPrompt }),
      setReferenceImage: (file: File | null) => set({ referenceImage: file }),
      setReferenceImagePreview: (preview: string | null) => set({ referenceImagePreview: preview }),
      setError: (error: string | null) => set({ error }),
      clearAll: () => set({
        generatedImage: null,
        prompt: '',
        refinementPrompt: '',
        referenceImage: null,
        referenceImagePreview: null,
        error: null,
        isGenerating: false,
      }),
    }),
    {
      name: 'gemini-store',
      // 只持久化部分狀態
      partialize: (state) => ({
        apiKey: state.apiKey,
        isApiKeyValid: state.isApiKeyValid,
        selectedModel: state.selectedModel,
      }),
    }
  )
);

// 便利的 hooks
export const useApiKey = () => {
  const apiKey = useGeminiStore((state) => state.apiKey);
  const isApiKeyValid = useGeminiStore((state) => state.isApiKeyValid);
  const setApiKey = useGeminiStore((state) => state.setApiKey);
  const setApiKeyValid = useGeminiStore((state) => state.setApiKeyValid);

  return { apiKey, isApiKeyValid, setApiKey, setApiKeyValid };
};

export const useModel = () => {
  const selectedModel = useGeminiStore((state) => state.selectedModel);
  const setSelectedModel = useGeminiStore((state) => state.setSelectedModel);

  return { selectedModel, setSelectedModel };
};

export const useImageGeneration = () => {
  const isGenerating = useGeminiStore((state) => state.isGenerating);
  const generatedImage = useGeminiStore((state) => state.generatedImage);
  const setIsGenerating = useGeminiStore((state) => state.setIsGenerating);
  const setGeneratedImage = useGeminiStore((state) => state.setGeneratedImage);

  return { isGenerating, generatedImage, setIsGenerating, setGeneratedImage };
};

export const usePrompts = () => {
  const prompt = useGeminiStore((state) => state.prompt);
  const refinementPrompt = useGeminiStore((state) => state.refinementPrompt);
  const setPrompt = useGeminiStore((state) => state.setPrompt);
  const setRefinementPrompt = useGeminiStore((state) => state.setRefinementPrompt);

  return { prompt, refinementPrompt, setPrompt, setRefinementPrompt };
};

export const useReferenceImage = () => {
  const referenceImage = useGeminiStore((state) => state.referenceImage);
  const referenceImagePreview = useGeminiStore((state) => state.referenceImagePreview);
  const setReferenceImage = useGeminiStore((state) => state.setReferenceImage);
  const setReferenceImagePreview = useGeminiStore((state) => state.setReferenceImagePreview);

  return { referenceImage, referenceImagePreview, setReferenceImage, setReferenceImagePreview };
};

export const useError = () => {
  const error = useGeminiStore((state) => state.error);
  const setError = useGeminiStore((state) => state.setError);

  return { error, setError };
};


